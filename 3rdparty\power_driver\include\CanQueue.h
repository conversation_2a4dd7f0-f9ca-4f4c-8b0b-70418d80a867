#ifndef can_queue_H
#define can_queue_H

#ifdef __cplusplus
#if __cplusplus
  extern "C" {
#endif
#endif /* __cplusplus */
#include <stdint.h>
#include "CanFrame.h"



uint8_t CanQueueInit( uint8_t port);//can���г�ʼ��

uint8_t CAN_WritePortMsg(uint8_t port, CanFrame *rxMsg);//������д�뵽���ն�����
uint8_t CAN_GetPortMsg(uint8_t port, CanFrame *rxMsg);//��ȡ���ն����е�����

void CanFrameSend( uint8_t port, const CanFrame* can_msg );//��msgд�뵽�����Ͷ�����
void CanFrameSendRightNow( uint8_t port, const CanFrame* can_msg );//��msgд�뵽�������Ͷ�����
uint8_t CAN_SendingPortMsg(uint8_t port, CanFrame *rxMsg);//�������Ͷ����е�����ȡ�������ڷ���

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif

